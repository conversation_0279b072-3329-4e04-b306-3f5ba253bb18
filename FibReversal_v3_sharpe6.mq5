//+------------------------------------------------------------------+
//|                                           Fibonacci Reversal EA |
//|                          Fibonacci-based reversal trading strategy |
//+------------------------------------------------------------------+
#property version     "1.0"
#property description "Fibonacci Reversal Strategy with fractal detection and Fibonacci retracement levels"

#define FibReversalMagic 9765427

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Strategy parameters
input double InpPriceChangePercentage = 3;          // Price change threshold percentage
input double InpFibonacciLevel       = 0.236;         // Fibonacci retracement level for entries
input double InpHardStopPercentage   = 1.5;           // Hard stop percentage
input bool   InpUseFixedLotSize      = false;         // Use fixed lot size instead of risk percentage
input double InpFixedLotSize         = 0.01;          // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent          = 2.0;           // Risk percentage per trade (when InpUseFixedLotSize = false)

// Trading phases
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE,    // Fibonacci detection phase
   PHASE_ACTIVE_LONG, // Active phase searching for long entries
   PHASE_ACTIVE_SHORT // Active phase searching for short entries
};

//+------------------------------------------------------------------+
//| Fibonacci Reversal Expert Advisor class                         |
//+------------------------------------------------------------------+
class CFibReversalExpert
  {
protected:
   // Trading objects
   double            m_adjusted_point;             // Point value adjusted for 3 or 5 digits
   CSymbolInfo       m_symbol;                     // Symbol info object
   CTrade            m_trade;                      // Trading object
   CPositionInfo     m_position;                   // Position info object
   CAccountInfo      m_account;                    // Account info wrapper

   // Indicator handles
   int               m_handle_fractals;            // Fractals indicator handle

   // Forward-looking fractal detection variables
   datetime          m_lastProcessedBarTime;       // Time of last processed 4H bar
   bool              m_waitingForNewBar;           // Flag to wait for new 4H bar after position exit

   // Trading variables
   double            m_priceSens;                  // Price sensitivity
   ulong             m_longTicket;                 // Long position ticket
   ulong             m_shortTicket;                // Short position ticket
   double            m_longEntryPrice;             // Long position entry price
   double            m_shortEntryPrice;            // Short position entry price

   // Fibonacci strategy variables (Python style)
   ENUM_TRADING_PHASE m_tradingPhase;             // Current trading phase
   double            m_fibHighPrice;               // High price for Fibonacci calculation
   double            m_fibLowPrice;                // Low price for Fibonacci calculation
   double            m_fibLevel236;                // Fibonacci 0.236 level
   double            m_fibLevel500;                // Fibonacci 0.5 level

   // Heartbeat logging variables
   datetime          m_lastHeartbeatTime;          // Last heartbeat timestamp
   int               m_heartbeatInterval;          // Heartbeat interval in seconds (1 hour)

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   bool              Init(void);
   bool              Processing(void);
   ENUM_TRADING_PHASE GetTradingPhase(void) { return m_tradingPhase; }

protected:
   bool              InitCheckParameters(const int digits_adjust);
   bool              InitIndicators(void);
   bool              ProcessPassivePhase(void);
   bool              ProcessActivePhase(void);
   bool              DetectFractals(void);
   bool              UpdatePeakTroughTracking(void);
   void              CalculateFibonacciLevels(void);
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   double            CalculateLotSize(double stopLossDistance);
   void              ResetStrategy(void);
   void              LogHeartbeat(void);
  };
//--- global expert
CFibReversalExpert ExtExpert;
//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_adjusted_point(0),
                                               m_handle_fractals(INVALID_HANDLE),
                                               m_priceSens(0),
                                               m_longTicket(-1),
                                               m_shortTicket(-1),
                                               m_longEntryPrice(-1),
                                               m_shortEntryPrice(-1),
                                               m_lastProcessedBarTime(0),
                                               m_waitingForNewBar(false),
                                               m_tradingPhase(PHASE_PASSIVE),
                                               m_fibHighPrice(0),
                                               m_fibLowPrice(0),
                                               m_fibLevel236(0),
                                               m_fibLevel500(0),
                                               m_lastHeartbeatTime(0),
                                               m_heartbeatInterval(3600)
  {
   // No array initialization needed for forward-looking approach
  }
//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
  {
  }
//+------------------------------------------------------------------+
//| Initialization and checking for input parameters                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Init(void)
  {
   // Initialize trading objects
   m_symbol.Name(Symbol());
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   m_priceSens = MathPow(10, -1 * m_symbol.Digits());

   // Adjust point value for 3 or 5 digit brokers
   int digits_adjust = 1;
   if(m_symbol.Digits() == 3 || m_symbol.Digits() == 5)
      digits_adjust = 10;
   m_adjusted_point = m_symbol.Point() * digits_adjust;

   // Set trading deviation
   m_trade.SetDeviationInPoints(3 * digits_adjust);

   // Initialize strategy state
   ResetStrategy();

//---
   if(!InitCheckParameters(digits_adjust))
      return(false);
   if(!InitIndicators())
      return(false);

   // Log initial heartbeat on EA startup
   printf("=== EA INITIALIZED === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("EA: Fibonacci Reversal Strategy started successfully");
   printf("Parameters: Risk=%.1f%%, Price Change=%.1f%%, Fib Level=%.3f, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpFibonacciLevel, InpHardStopPercentage);
   printf("Heartbeat logging enabled - will report status every hour");

//--- succeed
   return(true);
  }
//+------------------------------------------------------------------+
//| Checking for input parameters                                    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitCheckParameters(const int digits_adjust)
  {
   // Validate risk percentage (only when not using fixed lot size)
   if(!InpUseFixedLotSize && (InpRiskPercent <= 0 || InpRiskPercent > 100))
     {
      printf("Risk percentage must be between 0 and 100, current value: %f", InpRiskPercent);
      return(false);
     }

   // Validate fixed lot size (only when using fixed lot size)
   if(InpUseFixedLotSize && InpFixedLotSize <= 0)
     {
      printf("Fixed lot size must be greater than 0, current value: %f", InpFixedLotSize);
      return(false);
     }

   // Validate price change percentage
   if(InpPriceChangePercentage <= 0)
     {
      printf("Price change percentage must be greater than 0, current value: %f", InpPriceChangePercentage);
      return(false);
     }

   // No lookback period validation needed for forward-looking approach

   // Validate Fibonacci level
   if(InpFibonacciLevel <= 0 || InpFibonacciLevel >= 1)
     {
      printf("Fibonacci level must be between 0 and 1, current value: %f", InpFibonacciLevel);
      return(false);
     }

   // Validate hard stop percentage
   if(InpHardStopPercentage <= 0)
     {
      printf("Hard stop percentage must be greater than 0, current value: %f", InpHardStopPercentage);
      return(false);
     }

   // Validate fixed lot size against symbol specifications
   if(InpUseFixedLotSize)
     {
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      if(InpFixedLotSize < minLot)
        {
         printf("Fixed lot size (%f) is below minimum allowed (%f)", InpFixedLotSize, minLot);
         return(false);
        }

      if(InpFixedLotSize > maxLot)
        {
         printf("Fixed lot size (%f) is above maximum allowed (%f)", InpFixedLotSize, maxLot);
         return(false);
        }

      printf("Fixed lot size validation: Input=%f, Min=%f, Max=%f, Step=%f", InpFixedLotSize, minLot, maxLot, lotStep);
     }

   return(true);
  }
//+------------------------------------------------------------------+
//| Initialization of the indicators                                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitIndicators(void)
  {
   // Create Fractals indicator on 4H timeframe
   if(m_handle_fractals == INVALID_HANDLE)
      if((m_handle_fractals = iFractals(NULL, PERIOD_H4)) == INVALID_HANDLE)
      {
         printf("Error creating Fractals indicator on 4H timeframe");
         return(false);
      }

   printf("DEBUG: Fractals indicator created successfully on 4H timeframe, handle: %d", m_handle_fractals);

   // Wait for indicator to calculate initial values
   Sleep(1000);

   return(true);
  }
//+------------------------------------------------------------------+
//| Reset strategy to passive phase (Python style)                 |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetStrategy(void)
{
   m_tradingPhase = PHASE_PASSIVE;
   m_fibHighPrice = 0;
   m_fibLowPrice = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_waitingForNewBar = true; // Wait for new 4H bar after reset
   printf("Strategy reset to passive phase - waiting for new 4H bar");
}

//+------------------------------------------------------------------+
//| Check for long position closing (Python style)                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongClosed(void)
{
   bool res = false;

   if(!m_position.SelectByTicket(m_longTicket))
      return false;

   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0); // Current bar high (just completed)
   double currentLow = iLow(NULL, PERIOD_H4, 0);   // Current bar low (just completed)

   // Python exit conditions:
   // 1. Take profit: row['High'] >= aktif_fib.level_500
   bool tpHit = (currentHigh >= m_fibLevel500);

   // 2. Hard stop: row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 - InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentLow < hardStopLevel);

   if(tpHit || hardStopHit)
   {
      // Calculate profit percentage (Python style)
      double profitPercent = 0;
      if(tpHit)
      {
         profitPercent = (m_fibLevel500 - m_fibLevel236) / m_fibLevel236 * 100.0;
         printf("Closing long position: TP hit - High %.5f >= Fib 500 %.5f, Profit: %.2f%%",
                currentHigh, m_fibLevel500, profitPercent);
      }
      if(hardStopHit)
      {
         profitPercent = -InpHardStopPercentage;
         printf("Closing long position: Hard stop hit - Low %.5f < Stop %.5f, Loss: %.2f%%",
                currentLow, hardStopLevel, profitPercent);
      }

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Long position closed successfully - %s", tpHit ? "Fib Completed" : "Hard Stop");
         m_longTicket = -1;
         m_longEntryPrice = -1;
         ResetStrategy(); // Reset to passive phase
         m_waitingForNewBar = true; // Wait for new 4H bar before starting detection
         m_lastProcessedBarTime = iTime(NULL, PERIOD_H4, 0); // Store current bar time
      }
      else
         printf("Error closing long position: %s", m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Check for short position closing (Python style)                |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortClosed(void)
{
   bool res = false;

   if(!m_position.SelectByTicket(m_shortTicket))
      return false;

   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0); // Current bar high (just completed)
   double currentLow = iLow(NULL, PERIOD_H4, 0);   // Current bar low (just completed)

   // Python exit conditions:
   // 1. Take profit: row['Low'] <= aktif_fib.level_500
   bool tpHit = (currentLow <= m_fibLevel500);

   // 2. Hard stop: row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 + InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentHigh > hardStopLevel);

   if(tpHit || hardStopHit)
   {
      // Calculate profit percentage (Python style)
      double profitPercent = 0;
      if(tpHit)
      {
         profitPercent = (m_fibLevel236 - m_fibLevel500) / m_fibLevel236 * 100.0;
         printf("Closing short position: TP hit - Low %.5f <= Fib 500 %.5f, Profit: %.2f%%",
                currentLow, m_fibLevel500, profitPercent);
      }
      if(hardStopHit)
      {
         profitPercent = -InpHardStopPercentage;
         printf("Closing short position: Hard stop hit - High %.5f > Stop %.5f, Loss: %.2f%%",
                currentHigh, hardStopLevel, profitPercent);
      }

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Short position closed successfully - %s", tpHit ? "Fib Completed" : "Hard Stop");
         m_shortTicket = -1;
         m_shortEntryPrice = -1;
         ResetStrategy(); // Reset to passive phase
         m_waitingForNewBar = true; // Wait for new 4H bar before starting detection
         m_lastProcessedBarTime = iTime(NULL, PERIOD_H4, 0); // Store current bar time
      }
      else
         printf("Error closing short position: %s", m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage or fixed lot size    |
//+------------------------------------------------------------------+
double CFibReversalExpert::CalculateLotSize(double stopLossDistance)
{
   double lotSize = 0;

   // Use fixed lot size if enabled
   if(InpUseFixedLotSize)
   {
      // Get symbol specifications for normalization
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Normalize the fixed lot size to valid step
      lotSize = MathRound(InpFixedLotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

      // Log if normalization occurred
      if(MathAbs(lotSize - InpFixedLotSize) > 0.0001)
      {
         printf("Fixed lot size normalized from %f to %f (step: %f)", InpFixedLotSize, lotSize, lotStep);
      }
   }
   else
   {
      // Calculate lot size based on risk percentage
      // Get account balance
      double accountBalance = m_account.Balance();

      // Calculate risk amount in account currency
      double riskAmount = accountBalance * (InpRiskPercent / 100.0);

      // Get symbol specifications
      double tickValue = m_symbol.TickValue();
      double tickSize = m_symbol.TickSize();

      if(tickSize > 0 && tickValue > 0)
      {
         // Calculate value per point
         double valuePerPoint = (tickValue / tickSize);

         // Calculate lot size based on risk
         if(stopLossDistance > 0)
            lotSize = riskAmount / (stopLossDistance * valuePerPoint);
      }

      // Normalize lot size to symbol specifications
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Round to lot step
      lotSize = MathRound(lotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   }

   return lotSize;
}

//+------------------------------------------------------------------+
//| Process passive phase - Python-style pattern detection         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessPassivePhase(void)
{
   if(m_tradingPhase != PHASE_PASSIVE)
      return false;

   // Check if we're waiting for a new 4H bar
   if(m_waitingForNewBar)
   {
      datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
      if(currentBarTime != m_lastProcessedBarTime)
      {
         // New bar has formed, we can start detection
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - ready to start pattern detection");
      }
      else
      {
         // Still waiting for new bar
         return false;
      }
   }

   return DetectFractals();
}

//+------------------------------------------------------------------+
//| Process active phase - Python-style peak/trough tracking       |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActivePhase(void)
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   // Update peak/trough tracking (Python style)
   bool rangeUpdated = UpdatePeakTroughTracking();

   // Check for position opening
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
      return LongOpened();
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
      return ShortOpened();

   return rangeUpdated;
}

//+------------------------------------------------------------------+
//| Python-style pattern detection using 3-bar close patterns      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectFractals(void)
{
   // Check if we need to wait for a new 4H bar
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
   if(m_waitingForNewBar)
   {
      if(currentBarTime == m_lastProcessedBarTime)
      {
         // Still the same bar, keep waiting
         return false;
      }
      else
      {
         // New bar has formed, we can start detection
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - starting pattern detection");
      }
   }

   // Check if we have enough 4H bars for 3-bar pattern detection
   int barsCount = iBars(NULL, PERIOD_H4);
   if(barsCount < 5)
   {
      static bool warningShown = false;
      if(!warningShown)
      {
         printf("DEBUG: Not enough 4H bars for pattern detection. Current: %d, Need: 5", barsCount);
         warningShown = true;
      }
      return false;
   }

   // Get the last 3 completed 4H close prices (Python style: close0, close1, close2)
   double close0 = iClose(NULL, PERIOD_H4, 2); // 2 bars ago
   double close1 = iClose(NULL, PERIOD_H4, 1); // 1 bar ago
   double close2 = iClose(NULL, PERIOD_H4, 0); // Current bar (just completed)

   printf("DEBUG: 3-bar pattern check - close0=%.5f, close1=%.5f, close2=%.5f", close0, close1, close2);

   // Check for mode switching patterns (Python logic)
   bool bullishPattern = (close0 > close1 && close1 > close2); // Bearish trend → look for bullish fib
   bool bearishPattern = (close0 < close1 && close1 < close2); // Bullish trend → look for bearish fib

   // Python style: Only detect new patterns when NOT already in fibonacci tracking mode
   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   if(bullishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      printf("BULLISH PATTERN DETECTED: close0 > close1 > close2 - switching to look for bullish fib");
      m_tradingPhase = PHASE_ACTIVE_LONG;

      // Lock peak from last 3 bars (Python style) - this is the initial peak
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);

      m_fibHighPrice = MathMax(high0, MathMax(high1, high2));

      // Initialize trough to current low (will be updated as we find lower lows)
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_fibLowPrice = MathMin(low0, MathMin(low1, low2));

      printf("Peak locked at %.5f, initial trough at %.5f from 3-bar pattern", m_fibHighPrice, m_fibLowPrice);
      return true;
   }
   // Python style: Only detect new patterns when NOT already in fibonacci tracking mode
   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   else if(bearishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      printf("BEARISH PATTERN DETECTED: close0 < close1 < close2 - switching to look for bearish fib");
      m_tradingPhase = PHASE_ACTIVE_SHORT;

      // Lock trough from last 3 bars (Python style) - this is the initial trough
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_fibLowPrice = MathMin(low0, MathMin(low1, low2));

      // Initialize peak to current high (will be updated as we find higher highs)
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);

      m_fibHighPrice = MathMax(high0, MathMax(high1, high2));

      printf("Trough locked at %.5f, initial peak at %.5f from 3-bar pattern", m_fibLowPrice, m_fibHighPrice);
      return true;
   }

   return false;
}
//+------------------------------------------------------------------+
//| Calculate Fibonacci retracement levels                          |
//+------------------------------------------------------------------+
void CFibReversalExpert::CalculateFibonacciLevels(void)
{
   // Validate that we have valid high and low prices
   if(m_fibHighPrice <= 0 || m_fibLowPrice <= 0 || m_fibHighPrice <= m_fibLowPrice)
   {
      printf("ERROR: Invalid Fibonacci range - High: %.5f, Low: %.5f", m_fibHighPrice, m_fibLowPrice);
      return;
   }

   double fibRange = m_fibHighPrice - m_fibLowPrice;

   // Calculate Fibonacci retracement levels based on trading phase
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // For LONG setups (after price dropped): retracement UP from the low
      // 0.236 level: Low + (Range * 0.236) - price needs to retrace up from low
      // 0.5 level: Low + (Range * 0.5) - target level
      m_fibLevel236 = m_fibLowPrice + (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibLowPrice + (fibRange * 0.5);
      printf("LONG setup - Fibonacci levels calculated (retracement UP from low):");
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // For SHORT setups (after price rose): retracement DOWN from the high
      // 0.236 level: High - (Range * 0.236) - price needs to retrace down from high
      // 0.5 level: High - (Range * 0.5) - target level
      m_fibLevel236 = m_fibHighPrice - (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibHighPrice - (fibRange * 0.5);
      printf("SHORT setup - Fibonacci levels calculated (retracement DOWN from high):");
   }
   else
   {
      printf("ERROR: CalculateFibonacciLevels called in PASSIVE phase");
      return;
   }

   printf("  Range: %.5f (High: %.5f - Low: %.5f)", fibRange, m_fibHighPrice, m_fibLowPrice);
   printf("  Fib 0.236 (%.3f): %.5f", InpFibonacciLevel, m_fibLevel236);
   printf("  Fib 0.500: %.5f", m_fibLevel500);
   printf("  Current Bid: %.5f, Ask: %.5f", m_symbol.Bid(), m_symbol.Ask());
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking (Python style)                     |
//+------------------------------------------------------------------+
bool CFibReversalExpert::UpdatePeakTroughTracking(void)
{
   // Only update in active phase
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   // Get current bar data
   double currentHigh = iHigh(NULL, PERIOD_H4, 0); // Current bar (just completed)
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   datetime currentTime = iTime(NULL, PERIOD_H4, 0);

   bool rangeUpdated = false;

   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python style reset logic: Only reset if fibonacci candidate exists AND price moves outside range
      double currentClose = iClose(NULL, PERIOD_H4, 0);
      if(m_fibLevel236 > 0 && (currentClose < m_fibLowPrice || currentClose > m_fibHighPrice))
      {
         printf("RESET: Close %.5f outside range [%.5f - %.5f] with active fib - returning to passive",
                currentClose, m_fibLowPrice, m_fibHighPrice);
         m_tradingPhase = PHASE_PASSIVE;
         m_fibHighPrice = 0;
         m_fibLowPrice = 0;
         m_fibLevel236 = 0;
         m_fibLevel500 = 0;
         return rangeUpdated;
      }

      // Looking for bullish fib - continuously update peak like Python script
      // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat = row['High']
      if(currentHigh > m_fibHighPrice)
      {
         m_fibHighPrice = currentHigh;
         printf("Peak updated to %.5f (bullish mode)", m_fibHighPrice);
      }

      // Check if we meet the minimum range requirement using current peak and current low
      double priceChange = (m_fibHighPrice - currentLow) / currentLow * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(m_fibLowPrice != currentLow || m_fibLevel236 == 0)
         {
            m_fibLowPrice = currentLow;
            printf("Bullish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange);
            CalculateFibonacciLevels();
            rangeUpdated = true;
         }
      }
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python style reset logic: Only reset if fibonacci candidate exists AND price moves outside range
      double currentClose = iClose(NULL, PERIOD_H4, 0);
      if(m_fibLevel236 > 0 && (currentClose > m_fibHighPrice || currentClose < m_fibLowPrice))
      {
         printf("RESET: Close %.5f outside range [%.5f - %.5f] with active fib - returning to passive",
                currentClose, m_fibLowPrice, m_fibHighPrice);
         m_tradingPhase = PHASE_PASSIVE;
         m_fibHighPrice = 0;
         m_fibLowPrice = 0;
         m_fibLevel236 = 0;
         m_fibLevel500 = 0;
         return rangeUpdated;
      }

      // Looking for bearish fib - continuously update trough like Python script
      // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat = row['Low']
      if(currentLow < m_fibLowPrice)
      {
         m_fibLowPrice = currentLow;
         printf("Trough updated to %.5f (bearish mode)", m_fibLowPrice);
      }

      // Check if we meet the minimum range requirement using current high and current trough
      double priceChange = (currentHigh - m_fibLowPrice) / m_fibLowPrice * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != currentHigh || m_fibLevel236 == 0)
         {
            m_fibHighPrice = currentHigh;
            printf("Bearish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange);
            CalculateFibonacciLevels();
            rangeUpdated = true;
         }
      }
   }

   return rangeUpdated;
}
//+------------------------------------------------------------------+
//| Open long position (Python style - immediate activation)       |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   if(m_tradingPhase != PHASE_ACTIVE_LONG)
      return false;

   // Check if we have valid fibonacci levels
   if(m_fibLevel236 <= 0 || m_fibLevel500 <= 0)
      return false;

   // Python style activation: Check if close price crosses above 236 level
   double close4h = iClose(NULL, PERIOD_H4, 0); // Current bar close (just completed)

   if(close4h > m_fibLevel236)
   {
      printf("BULLISH ACTIVATION: Close %.5f > Fib 236 %.5f - opening long position", close4h, m_fibLevel236);

      double ask = m_symbol.Ask();

      // Calculate lot size based on hard stop distance
      double hardStopDistance = ask * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_longEntryPrice = ask;

      printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             ask, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Buy(lotSize, Symbol(), ask))
      {
         m_longTicket = m_trade.ResultOrder();
         printf("Long position opened successfully, ticket: %I64u", m_longTicket);
         return true;
      }
      else
      {
         printf("Error opening long position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
//+------------------------------------------------------------------+
//| Open short position (Python style - immediate activation)      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   if(m_tradingPhase != PHASE_ACTIVE_SHORT)
      return false;

   // Check if we have valid fibonacci levels
   if(m_fibLevel236 <= 0 || m_fibLevel500 <= 0)
      return false;

   // Python style activation: Check if close price crosses below 236 level
   double close4h = iClose(NULL, PERIOD_H4, 0); // Current bar close (just completed)

   if(close4h < m_fibLevel236)
   {
      printf("BEARISH ACTIVATION: Close %.5f < Fib 236 %.5f - opening short position", close4h, m_fibLevel236);

      double bid = m_symbol.Bid();

      // Calculate lot size based on hard stop distance
      double hardStopDistance = bid * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_shortEntryPrice = bid;

      printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             bid, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Sell(lotSize, Symbol(), bid))
      {
         m_shortTicket = m_trade.ResultOrder();
         printf("Short position opened successfully, ticket: %I64u", m_shortTicket);
         return true;
      }
      else
      {
         printf("Error opening short position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
//+------------------------------------------------------------------+
//| Log heartbeat with EA status and position information           |
//+------------------------------------------------------------------+
void CFibReversalExpert::LogHeartbeat(void)
{
   // Update heartbeat timestamp
   m_lastHeartbeatTime = TimeCurrent();

   // Get account information
   double balance = m_account.Balance();
   double equity = m_account.Equity();
   double freeMargin = m_account.FreeMargin();

   // Count positions for this symbol
   int posCount = 0;
   string posInfo = "";

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         m_position.SelectByIndex(i);
         posCount++;

         string posType = (m_position.PositionType() == POSITION_TYPE_BUY) ? "LONG" : "SHORT";
         double openPrice = m_position.PriceOpen();
         double lotSize = m_position.Volume();
         double profit = m_position.Profit();
         datetime openTime = m_position.Time();

         // Calculate SL/TP based on strategy
         double sl = 0;
         double tp = 0;

         if(m_position.PositionType() == POSITION_TYPE_BUY)
         {
            sl = m_longEntryPrice * (1.0 - InpHardStopPercentage / 100.0);
            tp = m_fibLevel500;
         }
         else
         {
            sl = m_shortEntryPrice * (1.0 + InpHardStopPercentage / 100.0);
            tp = m_fibLevel500;
         }

         // Calculate position duration
         int durationHours = (int)((TimeCurrent() - openTime) / 3600);
         int durationMins = (int)(((TimeCurrent() - openTime) % 3600) / 60);

         posInfo += StringFormat("%s: %.2f lots @ %.5f, P&L: %.2f, SL: %.5f, TP: %.5f, Duration: %dh%dm | ",
                                posType, lotSize, openPrice, profit, sl, tp, durationHours, durationMins);
      }
   }

   // Log heartbeat information
   printf("=== HEARTBEAT === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("Account: Balance=%.2f, Equity=%.2f, Free Margin=%.2f", balance, equity, freeMargin);
   printf("Positions: %d active | %s", posCount, (posCount > 0) ? posInfo : "No positions");

   // Log strategy status
   string phaseStr = (m_tradingPhase == PHASE_PASSIVE) ? "PASSIVE" :
                    (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT";

   if(m_tradingPhase == PHASE_PASSIVE)
   {
      // In passive phase
      if(m_waitingForNewBar)
         printf("Strategy: Phase=%s, Waiting for new 4H bar", phaseStr);
      else
         printf("Strategy: Phase=%s, Looking for 3-bar patterns", phaseStr);
   }
   else
   {
      // In active phase, show the current Fibonacci range
      printf("Strategy: Phase=%s, High=%.5f, Low=%.5f, Fib236=%.5f, Fib500=%.5f",
             phaseStr, m_fibHighPrice, m_fibLowPrice, m_fibLevel236, m_fibLevel500);
   }

   printf("Market: Bid=%.5f, Ask=%.5f, Spread=%.1f points",
          m_symbol.Bid(), m_symbol.Ask(), m_symbol.Spread());
   printf("EA Status: Risk=%.1f%%, Price Change=%.1f%%, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpHardStopPercentage);
   printf("=== END HEARTBEAT ===");
}
//+------------------------------------------------------------------+
//| main function returns true if any position processed             |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
  {
   // Refresh market data
   if(!m_symbol.RefreshRates())
      return(false);

   // Debug: Track processing calls
   static int processingCounter = 0;
   processingCounter++;
   if(processingCounter % 10000 == 0) // Every 10000 calls
   {
      printf("DEBUG: Processing called %d times, Current phase: %s",
             processingCounter,
             (m_tradingPhase == PHASE_PASSIVE) ? "PASSIVE" :
             (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT");
   }

   // Check for heartbeat logging (once per hour)
   datetime currentTime = TimeCurrent();
   if(m_lastHeartbeatTime == 0 || (currentTime - m_lastHeartbeatTime) >= m_heartbeatInterval)
   {
      LogHeartbeat();
   }

   // Count and identify positions for this symbol
   uint posNumber = 0;
   int posTotal = PositionsTotal();
   for(int i = 0; i < posTotal; i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         posNumber++;
         m_position.SelectByIndex(i);
         if(m_position.PositionType() == POSITION_TYPE_BUY)
            m_longTicket = m_position.Ticket();
         else if(m_position.PositionType() == POSITION_TYPE_SELL)
            m_shortTicket = m_position.Ticket();
      }
   }

   // Process positions based on count
   if(posNumber > 1)
   {
      Alert("Error: Multiple positions detected");
      return(false);
   }
   else if(posNumber == 1)
   {
      // Handle existing positions - check for closing conditions
      if(m_position.SelectByTicket(m_longTicket) && m_position.PositionType() == POSITION_TYPE_BUY)
      {
         if(LongClosed())
            return(true);
      }

      if(m_position.SelectByTicket(m_shortTicket) && m_position.PositionType() == POSITION_TYPE_SELL)
      {
         if(ShortClosed())
            return(true);
      }
   }
   else if(posNumber == 0)
   {
      // No positions - process strategy phases
      if(m_tradingPhase == PHASE_PASSIVE)
      {
         if(ProcessPassivePhase())
            return(true);
      }
      else
      {
         if(ProcessActivePhase())
            return(true);
      }
   }

   return(false);
  }
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit(void)
  {
//--- create all necessary objects
   if(!ExtExpert.Init())
      return(INIT_FAILED);
//--- succeed
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert tick handler (Python style - process every new 4H bar)  |
//+------------------------------------------------------------------+
void OnTick(void)
{
   // Process on every new 4H bar (Python style)
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);

   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      ExtExpert.Processing();
   }
}
