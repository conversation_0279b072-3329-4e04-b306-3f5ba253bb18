//+------------------------------------------------------------------+
//|                                           Fibonacci Reversal EA |
//|                          Fibonacci-based reversal trading strategy |
//+------------------------------------------------------------------+
#property version     "1.0"
#property description "Fibonacci Reversal Strategy with fractal detection and Fibonacci retracement levels"

#define FibReversalMagic 9765427

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Strategy parameters
input double InpPriceChangePercentage = 3;          // Price change threshold percentage
input double InpFibonacciLevel       = 0.236;         // Fibonacci retracement level for entries
input double InpHardStopPercentage   = 1.5;           // Hard stop percentage
input bool   InpUseFixedLotSize      = false;         // Use fixed lot size instead of risk percentage
input double InpFixedLotSize         = 0.01;          // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent          = 2.0;           // Risk percentage per trade (when InpUseFixedLotSize = false)

// Trading phases
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE,    // Fibonacci detection phase
   PHASE_ACTIVE_LONG, // Active phase searching for long entries
   PHASE_ACTIVE_SHORT // Active phase searching for short entries
};

//+------------------------------------------------------------------+
//| Fibonacci Reversal Expert Advisor class                         |
//+------------------------------------------------------------------+
class CFibReversalExpert
  {
protected:
   // Trading objects
   double            m_adjusted_point;             // Point value adjusted for 3 or 5 digits
   CSymbolInfo       m_symbol;                     // Symbol info object
   CTrade            m_trade;                      // Trading object
   CPositionInfo     m_position;                   // Position info object
   CAccountInfo      m_account;                    // Account info wrapper

   // Indicator handles
   int               m_handle_fractals;            // Fractals indicator handle

   // Forward-looking fractal detection variables
   datetime          m_lastProcessedBarTime;       // Time of last processed 4H bar
   bool              m_waitingForNewBar;           // Flag to wait for new 4H bar after position exit

   // Trading variables
   double            m_priceSens;                  // Price sensitivity
   ulong             m_longTicket;                 // Long position ticket
   ulong             m_shortTicket;                // Short position ticket
   double            m_longEntryPrice;             // Long position entry price
   double            m_shortEntryPrice;            // Short position entry price

   // Fibonacci strategy variables
   ENUM_TRADING_PHASE m_tradingPhase;             // Current trading phase
   double            m_firstFractalPrice;          // First fractal price in memory
   bool              m_firstFractalIsUp;           // True if first fractal is upward
   datetime          m_firstFractalTime;           // Timestamp of first fractal
   double            m_secondFractalPrice;         // Second fractal price (opposite direction)
   bool              m_secondFractalIsUp;          // True if second fractal is upward
   datetime          m_secondFractalTime;          // Timestamp of second fractal
   double            m_fibHighPrice;               // High price for Fibonacci calculation
   double            m_fibLowPrice;                // Low price for Fibonacci calculation
   double            m_fibLevel236;                // Fibonacci 0.236 level
   double            m_fibLevel500;                // Fibonacci 0.5 level
   // Current Fibonacci range fractal information for heartbeat
   double            m_currentLowFractal;          // Current low fractal price used for Fib range
   bool              m_currentLowIsUp;             // True if current low fractal is UP type
   double            m_currentHighFractal;         // Current high fractal price used for Fib range
   bool              m_currentHighIsUp;            // True if current high fractal is UP type
   bool              m_candle4hClosed;             // Flag for 4H candle close condition
   bool              m_waitingForTouch;            // Waiting for price to touch Fib level

   // Heartbeat logging variables
   datetime          m_lastHeartbeatTime;          // Last heartbeat timestamp
   int               m_heartbeatInterval;          // Heartbeat interval in seconds (1 hour)

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   bool              Init(void);
   bool              Processing(void);
   ENUM_TRADING_PHASE GetTradingPhase(void) { return m_tradingPhase; }

protected:
   bool              InitCheckParameters(const int digits_adjust);
   bool              InitIndicators(void);
   bool              ProcessPassivePhase(void);
   bool              ProcessActivePhase(void);
   bool              DetectFractals(void);
   bool              UpdateFibonacciRange(void);
   void              CalculateFibonacciLevels(void);
   bool              Check4HCandleClose(bool isLong);
   bool              CheckPriceTouch(double level, bool fromBelow);
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   double            CalculateLotSize(double stopLossDistance);
   void              ResetStrategy(void);
   void              LogHeartbeat(void);
   void              EvaluateTradingPhase(void);
   bool              ProcessNewFractal(double price, bool isUp, datetime time);
   void              UpdateFibonacciRangeFromFractals(void);
  };
//--- global expert
CFibReversalExpert ExtExpert;
//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_adjusted_point(0),
                                               m_handle_fractals(INVALID_HANDLE),
                                               m_priceSens(0),
                                               m_longTicket(-1),
                                               m_shortTicket(-1),
                                               m_longEntryPrice(-1),
                                               m_shortEntryPrice(-1),
                                               m_lastProcessedBarTime(0),
                                               m_waitingForNewBar(false),
                                               m_tradingPhase(PHASE_PASSIVE),
                                               m_firstFractalPrice(0),
                                               m_firstFractalIsUp(false),
                                               m_firstFractalTime(0),
                                               m_secondFractalPrice(0),
                                               m_secondFractalIsUp(false),
                                               m_secondFractalTime(0),
                                               m_fibHighPrice(0),
                                               m_fibLowPrice(0),
                                               m_fibLevel236(0),
                                               m_fibLevel500(0),
                                               m_currentLowFractal(0),
                                               m_currentLowIsUp(false),
                                               m_currentHighFractal(0),
                                               m_currentHighIsUp(false),
                                               m_candle4hClosed(false),
                                               m_waitingForTouch(false),
                                               m_lastHeartbeatTime(0),
                                               m_heartbeatInterval(3600)
  {
   // No array initialization needed for forward-looking approach
  }
//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
  {
  }
//+------------------------------------------------------------------+
//| Initialization and checking for input parameters                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Init(void)
  {
   // Initialize trading objects
   m_symbol.Name(Symbol());
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   m_priceSens = MathPow(10, -1 * m_symbol.Digits());

   // Adjust point value for 3 or 5 digit brokers
   int digits_adjust = 1;
   if(m_symbol.Digits() == 3 || m_symbol.Digits() == 5)
      digits_adjust = 10;
   m_adjusted_point = m_symbol.Point() * digits_adjust;

   // Set trading deviation
   m_trade.SetDeviationInPoints(3 * digits_adjust);

   // Initialize strategy state
   ResetStrategy();

//---
   if(!InitCheckParameters(digits_adjust))
      return(false);
   if(!InitIndicators())
      return(false);

   // Log initial heartbeat on EA startup
   printf("=== EA INITIALIZED === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("EA: Fibonacci Reversal Strategy started successfully");
   printf("Parameters: Risk=%.1f%%, Price Change=%.1f%%, Fib Level=%.3f, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpFibonacciLevel, InpHardStopPercentage);
   printf("Heartbeat logging enabled - will report status every hour");

//--- succeed
   return(true);
  }
//+------------------------------------------------------------------+
//| Checking for input parameters                                    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitCheckParameters(const int digits_adjust)
  {
   // Validate risk percentage (only when not using fixed lot size)
   if(!InpUseFixedLotSize && (InpRiskPercent <= 0 || InpRiskPercent > 100))
     {
      printf("Risk percentage must be between 0 and 100, current value: %f", InpRiskPercent);
      return(false);
     }

   // Validate fixed lot size (only when using fixed lot size)
   if(InpUseFixedLotSize && InpFixedLotSize <= 0)
     {
      printf("Fixed lot size must be greater than 0, current value: %f", InpFixedLotSize);
      return(false);
     }

   // Validate price change percentage
   if(InpPriceChangePercentage <= 0)
     {
      printf("Price change percentage must be greater than 0, current value: %f", InpPriceChangePercentage);
      return(false);
     }

   // No lookback period validation needed for forward-looking approach

   // Validate Fibonacci level
   if(InpFibonacciLevel <= 0 || InpFibonacciLevel >= 1)
     {
      printf("Fibonacci level must be between 0 and 1, current value: %f", InpFibonacciLevel);
      return(false);
     }

   // Validate hard stop percentage
   if(InpHardStopPercentage <= 0)
     {
      printf("Hard stop percentage must be greater than 0, current value: %f", InpHardStopPercentage);
      return(false);
     }

   // Validate fixed lot size against symbol specifications
   if(InpUseFixedLotSize)
     {
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      if(InpFixedLotSize < minLot)
        {
         printf("Fixed lot size (%f) is below minimum allowed (%f)", InpFixedLotSize, minLot);
         return(false);
        }

      if(InpFixedLotSize > maxLot)
        {
         printf("Fixed lot size (%f) is above maximum allowed (%f)", InpFixedLotSize, maxLot);
         return(false);
        }

      printf("Fixed lot size validation: Input=%f, Min=%f, Max=%f, Step=%f", InpFixedLotSize, minLot, maxLot, lotStep);
     }

   return(true);
  }
//+------------------------------------------------------------------+
//| Initialization of the indicators                                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitIndicators(void)
  {
   // Create Fractals indicator on 4H timeframe
   if(m_handle_fractals == INVALID_HANDLE)
      if((m_handle_fractals = iFractals(NULL, PERIOD_H4)) == INVALID_HANDLE)
      {
         printf("Error creating Fractals indicator on 4H timeframe");
         return(false);
      }

   printf("DEBUG: Fractals indicator created successfully on 4H timeframe, handle: %d", m_handle_fractals);

   // Wait for indicator to calculate initial values
   Sleep(1000);

   return(true);
  }
//+------------------------------------------------------------------+
//| Reset strategy to passive phase                                  |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetStrategy(void)
{
   m_tradingPhase = PHASE_PASSIVE;
   // Keep fractal information with their original timestamps
   // m_firstFractalPrice = 0;
   // m_firstFractalIsUp = false;
   // m_firstFractalTime = 0;
   // m_secondFractalPrice = 0;
   // m_secondFractalIsUp = false;
   // m_secondFractalTime = 0;
   m_fibHighPrice = 0;
   m_fibLowPrice = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_currentLowFractal = 0;
   m_currentLowIsUp = false;
   m_currentHighFractal = 0;
   m_currentHighIsUp = false;
   m_candle4hClosed = false;
   m_waitingForTouch = false;
   m_waitingForNewBar = true; // Wait for new 4H bar after reset
   printf("Strategy reset to passive phase - keeping fractal data - waiting for new 4H bar");
}

//+------------------------------------------------------------------+
//| Check for long position closing                                  |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongClosed(void)
{
   bool res = false;
   double bid = m_symbol.Bid();

   if(!m_position.SelectByTicket(m_longTicket))
      return false;

   // Check exit conditions
   // 1. Price reaches Fibonacci 0.5 level (take profit) - from below for long positions
   bool tpHit = CheckPriceTouch(m_fibLevel500, true); // fromBelow = true for long TP

   // 2. Hard stop loss hit
   double hardStopPrice = m_longEntryPrice * (1.0 - InpHardStopPercentage / 100.0);
   bool hardStopHit = (bid <= hardStopPrice);

   // 3. Soft stop: 4H candle closes below 0.236 level
   bool softStopHit = false;
   static datetime lastSoftStopCheck = 0;
   datetime currentCandleTime = iTime(NULL, PERIOD_H4, 1); // Time of the last completed candle

   // Only check soft stop when a new completed candle is available
   if(currentCandleTime != lastSoftStopCheck)
   {
      lastSoftStopCheck = currentCandleTime;
      double close4h = iClose(NULL, PERIOD_H4, 1); // Get the completed candle close
      softStopHit = (close4h < m_fibLevel236);
      if(softStopHit)
         printf("DEBUG: Soft stop triggered - 4H candle closed below Fib 0.236 (%.5f < %.5f)", close4h, m_fibLevel236);
   }

   if(tpHit || hardStopHit || softStopHit)
   {
      // Debug logging
      if(tpHit)
         printf("Closing long position: TP hit at Fib 0.5 level (%.5f)", m_fibLevel500);
      if(hardStopHit)
         printf("Closing long position: Hard stop hit (bid=%.5f, stop=%.5f)", bid, hardStopPrice);
      if(softStopHit)
         printf("Closing long position: Soft stop - 4H close below Fib 0.236");

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Long position closed successfully");
         m_longTicket = -1;
         m_longEntryPrice = -1;
         // Clear fractal data for fresh start after position close
         m_firstFractalPrice = 0;
         m_firstFractalIsUp = false;
         m_firstFractalTime = 0;
         m_secondFractalPrice = 0;
         m_secondFractalIsUp = false;
         m_secondFractalTime = 0;
         ResetStrategy(); // Reset to passive phase
         m_waitingForNewBar = true; // Wait for new 4H bar before starting detection
         m_lastProcessedBarTime = iTime(NULL, PERIOD_H4, 0); // Store current bar time
      }
      else
         printf("Error closing long position: %s", m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Check for short position closing                                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortClosed(void)
{
   bool res = false;
   double ask = m_symbol.Ask();

   if(!m_position.SelectByTicket(m_shortTicket))
      return false;

   // Check exit conditions
   // 1. Price reaches Fibonacci 0.5 level (take profit) - from above for short positions
   bool tpHit = CheckPriceTouch(m_fibLevel500, false); // fromBelow = false for short TP

   // 2. Hard stop loss hit
   double hardStopPrice = m_shortEntryPrice * (1.0 + InpHardStopPercentage / 100.0);
   bool hardStopHit = (ask >= hardStopPrice);

   // 3. Soft stop: 4H candle closes above 0.236 level
   bool softStopHit = false;
   static datetime lastSoftStopCheckShort = 0;
   datetime currentCandleTime = iTime(NULL, PERIOD_H4, 1); // Time of the last completed candle

   // Only check soft stop when a new completed candle is available
   if(currentCandleTime != lastSoftStopCheckShort)
   {
      lastSoftStopCheckShort = currentCandleTime;
      double close4h = iClose(NULL, PERIOD_H4, 1); // Get the completed candle close
      softStopHit = (close4h > m_fibLevel236);
      if(softStopHit)
         printf("DEBUG: Soft stop triggered - 4H candle closed above Fib 0.236 (%.5f > %.5f)", close4h, m_fibLevel236);
   }

   if(tpHit || hardStopHit || softStopHit)
   {
      // Debug logging
      if(tpHit)
         printf("Closing short position: TP hit at Fib 0.5 level (%.5f)", m_fibLevel500);
      if(hardStopHit)
         printf("Closing short position: Hard stop hit (ask=%.5f, stop=%.5f)", ask, hardStopPrice);
      if(softStopHit)
         printf("Closing short position: Soft stop - 4H close above Fib 0.236");

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Short position closed successfully");
         m_shortTicket = -1;
         m_shortEntryPrice = -1;
         // Clear fractal data for fresh start after position close
         m_firstFractalPrice = 0;
         m_firstFractalIsUp = false;
         m_firstFractalTime = 0;
         m_secondFractalPrice = 0;
         m_secondFractalIsUp = false;
         m_secondFractalTime = 0;
         ResetStrategy(); // Reset to passive phase
         m_waitingForNewBar = true; // Wait for new 4H bar before starting detection
         m_lastProcessedBarTime = iTime(NULL, PERIOD_H4, 0); // Store current bar time
      }
      else
         printf("Error closing short position: %s", m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage or fixed lot size    |
//+------------------------------------------------------------------+
double CFibReversalExpert::CalculateLotSize(double stopLossDistance)
{
   double lotSize = 0;

   // Use fixed lot size if enabled
   if(InpUseFixedLotSize)
   {
      // Get symbol specifications for normalization
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Normalize the fixed lot size to valid step
      lotSize = MathRound(InpFixedLotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

      // Log if normalization occurred
      if(MathAbs(lotSize - InpFixedLotSize) > 0.0001)
      {
         printf("Fixed lot size normalized from %f to %f (step: %f)", InpFixedLotSize, lotSize, lotStep);
      }
   }
   else
   {
      // Calculate lot size based on risk percentage
      // Get account balance
      double accountBalance = m_account.Balance();

      // Calculate risk amount in account currency
      double riskAmount = accountBalance * (InpRiskPercent / 100.0);

      // Get symbol specifications
      double tickValue = m_symbol.TickValue();
      double tickSize = m_symbol.TickSize();

      if(tickSize > 0 && tickValue > 0)
      {
         // Calculate value per point
         double valuePerPoint = (tickValue / tickSize);

         // Calculate lot size based on risk
         if(stopLossDistance > 0)
            lotSize = riskAmount / (stopLossDistance * valuePerPoint);
      }

      // Normalize lot size to symbol specifications
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Round to lot step
      lotSize = MathRound(lotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   }

   return lotSize;
}

//+------------------------------------------------------------------+
//| Process passive phase - Forward-looking fractal detection       |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessPassivePhase(void)
{
   if(m_tradingPhase != PHASE_PASSIVE)
      return false;

   // Check if we're waiting for a new 4H bar
   if(m_waitingForNewBar)
   {
      datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
      if(currentBarTime != m_lastProcessedBarTime)
      {
         // New bar has formed, we can start detection
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - ready to start fractal detection");
      }
      else
      {
         // Still waiting for new bar
         return false;
      }
   }

   return DetectFractals();
}

//+------------------------------------------------------------------+
//| Process active phase - Look for entries                         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActivePhase(void)
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   // First, continuously check if current range still meets the price change threshold
   if(m_firstFractalPrice != 0 && m_secondFractalPrice != 0)
   {
      double priceChange = MathAbs(m_firstFractalPrice - m_secondFractalPrice) / MathMin(m_firstFractalPrice, m_secondFractalPrice) * 100.0;

      if(priceChange < InpPriceChangePercentage)
      {
         printf("Current range %.2f%% below threshold %.2f%% - resetting strategy",
                priceChange, InpPriceChangePercentage);
         ResetStrategy();
         return true;
      }
   }

   // Second, check if we need to update Fibonacci levels due to new fractals
   // This allows the range to extend in trending markets
   bool fibUpdated = UpdateFibonacciRange();

   // Check if price reaches 0.5 Fibonacci level - reset strategy if it does
   // Do this AFTER updating Fibonacci range to ensure we have current levels
   double currentPrice = (m_symbol.Bid() + m_symbol.Ask()) / 2.0;
   bool resetTriggered = false;

   // Only check for reset if we have valid Fibonacci levels
   if(m_fibLevel500 > 0)
   {
      if(m_tradingPhase == PHASE_ACTIVE_LONG)
      {
         // In long phase, check if price reaches 0.5 level from below
         if(currentPrice >= m_fibLevel500)
         {
            printf("LONG phase: Price reached 0.5 Fib level (%.5f >= %.5f) - resetting strategy",
                   currentPrice, m_fibLevel500);
            resetTriggered = true;
         }
      }
      else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
      {
         // In short phase, check if price reaches 0.5 level from above
         if(currentPrice <= m_fibLevel500)
         {
            printf("SHORT phase: Price reached 0.5 Fib level (%.5f <= %.5f) - resetting strategy",
                   currentPrice, m_fibLevel500);
            resetTriggered = true;
         }
      }
   }

   if(resetTriggered)
   {
      ResetStrategy();
      return true;
   }

   // Check for position opening
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
      return LongOpened();
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
      return ShortOpened();

   return fibUpdated; // Return true if we updated Fibonacci levels
}

//+------------------------------------------------------------------+
//| Forward-looking fractal detection                               |
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectFractals(void)
{
   // Check if we need to wait for a new 4H bar
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
   if(m_waitingForNewBar)
   {
      if(currentBarTime == m_lastProcessedBarTime)
      {
         // Still the same bar, keep waiting
         return false;
      }
      else
      {
         // New bar has formed, we can start detection
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - starting fractal detection");
      }
   }

   // Check if we have enough 4H bars for fractal calculation (fractals need at least 5 bars)
   int barsCount = iBars(NULL, PERIOD_H4);
   if(barsCount < 10)
   {
      static bool warningShown = false;
      if(!warningShown)
      {
         printf("DEBUG: Not enough 4H bars for fractal detection. Current: %d, Need: 10", barsCount);
         warningShown = true;
      }
      return false;
   }

   // Get the most recent fractal data (smaller range for forward-looking detection)
   double upFractals[6];
   double downFractals[6];

   if(CopyBuffer(m_handle_fractals, 0, 0, 6, upFractals) != 6)
   {
      printf("DEBUG: Failed to copy fractal up buffer");
      return false;
   }

   if(CopyBuffer(m_handle_fractals, 1, 0, 6, downFractals) != 6)
   {
      printf("DEBUG: Failed to copy fractal down buffer");
      return false;
   }

   // Check for new fractals - use smaller range to avoid looking too far back
   // Start from index 2 (2 bars ago) and check only 2-3 recent bars
   for(int i = 2; i < 5; i++)
   {
      bool upFractal = (upFractals[i] != EMPTY_VALUE && upFractals[i] != 0);
      bool downFractal = (downFractals[i] != EMPTY_VALUE && downFractals[i] != 0);

      if(upFractal || downFractal)
      {
         double fractalPrice = upFractal ? upFractals[i] : downFractals[i];
         bool fractalIsUp = upFractal;
         datetime fractalTime = iTime(NULL, PERIOD_H4, i);

         printf("DEBUG: Fractal found at index %d: %s fractal at %.5f",
                i, fractalIsUp ? "UP" : "DOWN", fractalPrice);

         // Always try to maintain both fractals and check range validity
         bool fractalProcessed = ProcessNewFractal(fractalPrice, fractalIsUp, fractalTime);

         if(fractalProcessed)
         {
            // Check if we have a valid fractal couple
            if(m_firstFractalPrice != 0 && m_secondFractalPrice != 0)
            {
               double priceChange = MathAbs(m_firstFractalPrice - m_secondFractalPrice) / MathMin(m_firstFractalPrice, m_secondFractalPrice) * 100.0;

               // Always update Fibonacci range and calculate levels for any 2 fractals
               UpdateFibonacciRangeFromFractals();
               EvaluateTradingPhase();
               CalculateFibonacciLevels();

               // Check if price has already reached 0.5 Fibonacci level - reset if it has
               double currentPrice = (m_symbol.Bid() + m_symbol.Ask()) / 2.0;
               bool resetTriggered = false;

               if(m_tradingPhase == PHASE_ACTIVE_LONG)
               {
                  // In long phase, check if price has already reached 0.5 level
                  if(currentPrice >= m_fibLevel500)
                  {
                     printf("LONG phase: Price already at/above 0.5 Fib level (%.5f >= %.5f) - resetting strategy",
                            currentPrice, m_fibLevel500);
                     resetTriggered = true;
                  }
               }
               else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
               {
                  // In short phase, check if price has already reached 0.5 level
                  if(currentPrice <= m_fibLevel500)
                  {
                     printf("SHORT phase: Price already at/below 0.5 Fib level (%.5f <= %.5f) - resetting strategy",
                            currentPrice, m_fibLevel500);
                     resetTriggered = true;
                  }
               }

               if(resetTriggered)
               {
                  ResetStrategy();
                  return false; // Return false to continue looking for new fractals
               }

               // Only proceed to active trading if price change meets threshold
               if(priceChange >= InpPriceChangePercentage)
               {
                  printf("Valid fractal couple with %.2f%% range (threshold: %.2f%%) - entering active phase", priceChange, InpPriceChangePercentage);
                  return true; // Enter active phase
               }
               else
               {
                  printf("Fractal couple range %.2f%% below threshold %.2f%% - continuing search", priceChange, InpPriceChangePercentage);
                  return false; // Continue looking for valid fractals
               }
            }
         }
      }
   }

   return false; // No valid fractal couple found yet
}
//+------------------------------------------------------------------+
//| Calculate Fibonacci retracement levels                          |
//+------------------------------------------------------------------+
void CFibReversalExpert::CalculateFibonacciLevels(void)
{
   // Validate that we have valid high and low prices
   if(m_fibHighPrice <= 0 || m_fibLowPrice <= 0 || m_fibHighPrice <= m_fibLowPrice)
   {
      printf("ERROR: Invalid Fibonacci range - High: %.5f, Low: %.5f", m_fibHighPrice, m_fibLowPrice);
      return;
   }

   double fibRange = m_fibHighPrice - m_fibLowPrice;

   // Calculate Fibonacci retracement levels based on trading phase
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // For LONG setups (after price dropped): retracement UP from the low
      // 0.236 level: Low + (Range * 0.236) - price needs to retrace up from low
      // 0.5 level: Low + (Range * 0.5) - target level
      m_fibLevel236 = m_fibLowPrice + (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibLowPrice + (fibRange * 0.5);
      printf("LONG setup - Fibonacci levels calculated (retracement UP from low):");
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // For SHORT setups (after price rose): retracement DOWN from the high
      // 0.236 level: High - (Range * 0.236) - price needs to retrace down from high
      // 0.5 level: High - (Range * 0.5) - target level
      m_fibLevel236 = m_fibHighPrice - (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibHighPrice - (fibRange * 0.5);
      printf("SHORT setup - Fibonacci levels calculated (retracement DOWN from high):");
   }
   else
   {
      printf("ERROR: CalculateFibonacciLevels called in PASSIVE phase");
      return;
   }

   printf("  Range: %.5f (High: %.5f - Low: %.5f)", fibRange, m_fibHighPrice, m_fibLowPrice);
   printf("  Fib 0.236 (%.3f): %.5f", InpFibonacciLevel, m_fibLevel236);
   printf("  Fib 0.500: %.5f", m_fibLevel500);
   printf("  Current Bid: %.5f, Ask: %.5f", m_symbol.Bid(), m_symbol.Ask());
}

//+------------------------------------------------------------------+
//| Update Fibonacci range in active phase if new fractals extend it|
//+------------------------------------------------------------------+
bool CFibReversalExpert::UpdateFibonacciRange(void)
{
   // Only update in active phase
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   // Get recent fractal data to check for new fractals
   double upFractals[6];
   double downFractals[6];

   if(CopyBuffer(m_handle_fractals, 0, 0, 6, upFractals) != 6)
      return false;
   if(CopyBuffer(m_handle_fractals, 1, 0, 6, downFractals) != 6)
      return false;

   bool rangeUpdated = false;

   // Check the most recent fractals - use smaller range to avoid old fractals
   for(int i = 2; i < 5; i++)
   {
      bool upFractal = (upFractals[i] != EMPTY_VALUE && upFractals[i] != 0);
      bool downFractal = (downFractals[i] != EMPTY_VALUE && downFractals[i] != 0);

      if(upFractal || downFractal)
      {
         double fractalPrice = upFractal ? upFractals[i] : downFractals[i];
         bool fractalIsUp = upFractal;
         datetime fractalTime = iTime(NULL, PERIOD_H4, i);

         // Process the new fractal using our unified approach
         bool fractalProcessed = ProcessNewFractal(fractalPrice, fractalIsUp, fractalTime);

         if(fractalProcessed)
         {
            rangeUpdated = true;
         }
      }
   }

   // If range was updated, recalculate everything including phase direction
   if(rangeUpdated)
   {
      // Update Fibonacci range from current fractals
      UpdateFibonacciRangeFromFractals();

      // Check if the updated range still meets the price change threshold
      double priceChange = MathAbs(m_firstFractalPrice - m_secondFractalPrice) / MathMin(m_firstFractalPrice, m_secondFractalPrice) * 100.0;

      if(priceChange < InpPriceChangePercentage)
      {
         printf("Updated range %.2f%% below threshold %.2f%% - resetting to passive phase",
                priceChange, InpPriceChangePercentage);
         ResetStrategy();
         return false;
      }

      // Store previous phase for comparison
      ENUM_TRADING_PHASE previousPhase = m_tradingPhase;

      // Re-evaluate trading phase
      EvaluateTradingPhase();

      // Check if phase changed
      if(previousPhase != m_tradingPhase)
      {
         printf("Trading phase changed from %s to %s",
                (previousPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT",
                (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT");
      }

      CalculateFibonacciLevels();
      // Reset entry conditions since the range and potentially phase changed
      m_candle4hClosed = false;
      m_waitingForTouch = false;
      printf("Fibonacci range updated - levels and phase recalculated (%.2f%% change)", priceChange);
   }

   return rangeUpdated;
}

//+------------------------------------------------------------------+
//| Check for 4H candle close condition                             |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Check4HCandleClose(bool isLong)
{
   static datetime last4HTime = 0;
   datetime current4HTime = iTime(NULL, PERIOD_H4, 0);

   // Debug: Print 4H candle check info occasionally
   static int candle4hDebugCounter = 0;
   candle4hDebugCounter++;
   if(candle4hDebugCounter % 1000 == 0)
   {
      double close4h = iClose(NULL, PERIOD_H4, 1);
      printf("DEBUG: 4H Candle check - Close: %.5f, Fib236: %.5f, IsLong: %s, Closed: %s",
             close4h, m_fibLevel236, isLong ? "true" : "false", m_candle4hClosed ? "true" : "false");
   }

   // Check if a new 4H candle has formed
   if(current4HTime != last4HTime && last4HTime != 0)
   {
      last4HTime = current4HTime;

      // Get the close price of the previous 4H candle
      double close4h = iClose(NULL, PERIOD_H4, 1);

      printf("DEBUG: New 4H candle formed, checking close condition");

      if(isLong)
      {
         // For long: check if 4H candle closed above Fib 0.236
         if(close4h > m_fibLevel236)
         {
            m_candle4hClosed = true;
            m_waitingForTouch = true;
            printf("4H candle closed above Fib 0.236 (%.5f > %.5f) - waiting for touch", close4h, m_fibLevel236);
            return true;
         }
      }
      else
      {
         // For short: check if 4H candle closed below Fib 0.236
         if(close4h < m_fibLevel236)
         {
            m_candle4hClosed = true;
            m_waitingForTouch = true;
            printf("4H candle closed below Fib 0.236 (%.5f < %.5f) - waiting for touch", close4h, m_fibLevel236);
            return true;
         }
      }
   }
   else if(last4HTime == 0)
   {
      last4HTime = current4HTime;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if price crosses a specific level from a specific direction|
//+------------------------------------------------------------------+
bool CFibReversalExpert::CheckPriceTouch(double level, bool fromBelow)
{
   static double lastBid = 0;
   static double lastAsk = 0;

   double currentBid = m_symbol.Bid();
   double currentAsk = m_symbol.Ask();

   // Initialize on first call
   if(lastBid == 0 || lastAsk == 0)
   {
      lastBid = currentBid;
      lastAsk = currentAsk;
      return false;
   }

   bool crossed = false;

   if(fromBelow)
   {
      // Check for crossing from below (price going up to touch level)
      // Use Ask for upward movement (buying pressure)
      if(lastAsk < level && currentAsk >= level)
      {
         crossed = true;
         printf("DEBUG: Ask crossed level %.5f from BELOW (%.5f -> %.5f)", level, lastAsk, currentAsk);
      }
   }
   else
   {
      // Check for crossing from above (price going down to touch level)
      // Use Bid for downward movement (selling pressure)
      if(lastBid > level && currentBid <= level)
      {
         crossed = true;
         printf("DEBUG: Bid crossed level %.5f from ABOVE (%.5f -> %.5f)", level, lastBid, currentBid);
      }
   }

   // Update last prices for next comparison
   lastBid = currentBid;
   lastAsk = currentAsk;

   return crossed;
}
//+------------------------------------------------------------------+
//| Open long position                                               |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   if(m_tradingPhase != PHASE_ACTIVE_LONG)
      return false;

   static int longOpenDebugCounter = 0;
   longOpenDebugCounter++;
   if(longOpenDebugCounter % 1000 == 0)
   {
      printf("DEBUG: LongOpened called %d times, 4HClosed: %s, WaitingForTouch: %s",
             longOpenDebugCounter, m_candle4hClosed ? "true" : "false", m_waitingForTouch ? "true" : "false");
   }

   // Check if 4H candle has closed above Fib 0.236
   if(!m_candle4hClosed)
   {
      Check4HCandleClose(true);
      return false;
   }

   // Check if price touches Fib 0.236 level from above (pullback after closing above)
   if(m_waitingForTouch && CheckPriceTouch(m_fibLevel236, false)) // fromBelow = false for long entry
   {
      printf("DEBUG: Price touched Fib 0.236 level from ABOVE, attempting to open long position");
      double ask = m_symbol.Ask();

      // Calculate stop loss (hard stop)
      double hardStopDistance = ask * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_longEntryPrice = ask;

      printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             ask, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Buy(lotSize, Symbol(), ask))
      {
         m_longTicket = m_trade.ResultOrder();
         m_waitingForTouch = false;
         printf("Long position opened successfully, ticket: %I64u", m_longTicket);
         return true;
      }
      else
      {
         printf("Error opening long position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
//+------------------------------------------------------------------+
//| Open short position                                              |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   if(m_tradingPhase != PHASE_ACTIVE_SHORT)
      return false;

   static int shortOpenDebugCounter = 0;
   shortOpenDebugCounter++;
   if(shortOpenDebugCounter % 1000 == 0)
   {
      printf("DEBUG: ShortOpened called %d times, 4HClosed: %s, WaitingForTouch: %s",
             shortOpenDebugCounter, m_candle4hClosed ? "true" : "false", m_waitingForTouch ? "true" : "false");
   }

   // Check if 4H candle has closed below Fib 0.236
   if(!m_candle4hClosed)
   {
      Check4HCandleClose(false);
      return false;
   }

   // Check if price touches Fib 0.236 level from below (pullback after closing below)
   if(m_waitingForTouch && CheckPriceTouch(m_fibLevel236, true)) // fromBelow = true for short entry
   {
      printf("DEBUG: Price touched Fib 0.236 level from BELOW, attempting to open short position");
      double bid = m_symbol.Bid();

      // Calculate stop loss (hard stop)
      double hardStopDistance = bid * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_shortEntryPrice = bid;

      printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             bid, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Sell(lotSize, Symbol(), bid))
      {
         m_shortTicket = m_trade.ResultOrder();
         m_waitingForTouch = false;
         printf("Short position opened successfully, ticket: %I64u", m_shortTicket);
         return true;
      }
      else
      {
         printf("Error opening short position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
//+------------------------------------------------------------------+
//| Log heartbeat with EA status and position information           |
//+------------------------------------------------------------------+
void CFibReversalExpert::LogHeartbeat(void)
{
   // Update heartbeat timestamp
   m_lastHeartbeatTime = TimeCurrent();

   // Get account information
   double balance = m_account.Balance();
   double equity = m_account.Equity();
   double freeMargin = m_account.FreeMargin();

   // Count positions for this symbol
   int posCount = 0;
   string posInfo = "";

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         m_position.SelectByIndex(i);
         posCount++;

         string posType = (m_position.PositionType() == POSITION_TYPE_BUY) ? "LONG" : "SHORT";
         double openPrice = m_position.PriceOpen();
         double lotSize = m_position.Volume();
         double profit = m_position.Profit();
         datetime openTime = m_position.Time();

         // Calculate SL/TP based on strategy
         double sl = 0;
         double tp = 0;

         if(m_position.PositionType() == POSITION_TYPE_BUY)
         {
            sl = m_longEntryPrice * (1.0 - InpHardStopPercentage / 100.0);
            tp = m_fibLevel500;
         }
         else
         {
            sl = m_shortEntryPrice * (1.0 + InpHardStopPercentage / 100.0);
            tp = m_fibLevel500;
         }

         // Calculate position duration
         int durationHours = (int)((TimeCurrent() - openTime) / 3600);
         int durationMins = (int)(((TimeCurrent() - openTime) % 3600) / 60);

         posInfo += StringFormat("%s: %.2f lots @ %.5f, P&L: %.2f, SL: %.5f, TP: %.5f, Duration: %dh%dm | ",
                                posType, lotSize, openPrice, profit, sl, tp, durationHours, durationMins);
      }
   }

   // Log heartbeat information
   printf("=== HEARTBEAT === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("Account: Balance=%.2f, Equity=%.2f, Free Margin=%.2f", balance, equity, freeMargin);
   printf("Positions: %d active | %s", posCount, (posCount > 0) ? posInfo : "No positions");

   // Log strategy status
   string phaseStr = (m_tradingPhase == PHASE_PASSIVE) ? "PASSIVE" :
                    (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT";

   if(m_tradingPhase == PHASE_PASSIVE)
   {
      // In passive phase, show the search fractals
      if(m_waitingForNewBar)
         printf("Strategy: Phase=%s, Waiting for new 4H bar", phaseStr);
      else if(m_firstFractalPrice != 0 && m_secondFractalPrice != 0)
         printf("Strategy: Phase=%s, FirstFractal=%.5f(%s), SecondFractal=%.5f(%s)",
                phaseStr,
                m_firstFractalPrice, m_firstFractalIsUp ? "UP" : "DOWN",
                m_secondFractalPrice, m_secondFractalIsUp ? "UP" : "DOWN");
      else if(m_firstFractalPrice != 0)
         printf("Strategy: Phase=%s, FirstFractal=%.5f(%s), SecondFractal=Not found",
                phaseStr, m_firstFractalPrice, m_firstFractalIsUp ? "UP" : "DOWN");
      else
         printf("Strategy: Phase=%s, No fractals found yet", phaseStr);
   }
   else
   {
      // In active phase, show the current Fibonacci range fractals
      printf("Strategy: Phase=%s, LowFractal=%.5f(%s), HighFractal=%.5f(%s), Fib236=%.5f, Fib500=%.5f, 4HClosed=%s, WaitingTouch=%s",
             phaseStr,
             m_currentLowFractal, m_currentLowIsUp ? "UP" : "DOWN",
             m_currentHighFractal, m_currentHighIsUp ? "UP" : "DOWN",
             m_fibLevel236, m_fibLevel500,
             m_candle4hClosed ? "YES" : "NO", m_waitingForTouch ? "YES" : "NO");
   }

   printf("Market: Bid=%.5f, Ask=%.5f, Spread=%.1f points",
          m_symbol.Bid(), m_symbol.Ask(), m_symbol.Spread());
   printf("EA Status: Risk=%.1f%%, Price Change=%.1f%%, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpHardStopPercentage);
   printf("=== END HEARTBEAT ===");
}
//+------------------------------------------------------------------+
//| Evaluate trading phase based on current fractal couple          |
//+------------------------------------------------------------------+
void CFibReversalExpert::EvaluateTradingPhase(void)
{
   // Only evaluate if we have both fractals
   if(m_firstFractalPrice == 0 || m_secondFractalPrice == 0)
      return;

   // Determine which fractal came first chronologically
   bool firstIsOlder = (m_firstFractalTime < m_secondFractalTime);

   if(firstIsOlder)
   {
      // First fractal is chronologically first
      if(!m_firstFractalIsUp && m_secondFractalIsUp) // DOWN then UP
      {
         m_tradingPhase = PHASE_ACTIVE_SHORT;
         printf("Phase evaluated: DOWN(%.5f) then UP(%.5f) - ACTIVE SHORT phase",
                m_firstFractalPrice, m_secondFractalPrice);
      }
      else if(m_firstFractalIsUp && !m_secondFractalIsUp) // UP then DOWN
      {
         m_tradingPhase = PHASE_ACTIVE_LONG;
         printf("Phase evaluated: UP(%.5f) then DOWN(%.5f) - ACTIVE LONG phase",
                m_firstFractalPrice, m_secondFractalPrice);
      }
   }
   else
   {
      // Second fractal is chronologically first
      if(!m_secondFractalIsUp && m_firstFractalIsUp) // DOWN then UP
      {
         m_tradingPhase = PHASE_ACTIVE_SHORT;
         printf("Phase evaluated: DOWN(%.5f) then UP(%.5f) - ACTIVE SHORT phase",
                m_secondFractalPrice, m_firstFractalPrice);
      }
      else if(m_secondFractalIsUp && !m_firstFractalIsUp) // UP then DOWN
      {
         m_tradingPhase = PHASE_ACTIVE_LONG;
         printf("Phase evaluated: UP(%.5f) then DOWN(%.5f) - ACTIVE LONG phase",
                m_secondFractalPrice, m_firstFractalPrice);
      }
   }
}





//+------------------------------------------------------------------+
//| main function returns true if any position processed             |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
  {
   // Refresh market data
   if(!m_symbol.RefreshRates())
      return(false);

   // Debug: Track processing calls
   static int processingCounter = 0;
   processingCounter++;
   if(processingCounter % 10000 == 0) // Every 10000 calls
   {
      printf("DEBUG: Processing called %d times, Current phase: %s",
             processingCounter,
             (m_tradingPhase == PHASE_PASSIVE) ? "PASSIVE" :
             (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT");
   }

   // Check for heartbeat logging (once per hour)
   datetime currentTime = TimeCurrent();
   if(m_lastHeartbeatTime == 0 || (currentTime - m_lastHeartbeatTime) >= m_heartbeatInterval)
   {
      LogHeartbeat();
   }

   // Count and identify positions for this symbol
   uint posNumber = 0;
   int posTotal = PositionsTotal();
   for(int i = 0; i < posTotal; i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         posNumber++;
         m_position.SelectByIndex(i);
         if(m_position.PositionType() == POSITION_TYPE_BUY)
            m_longTicket = m_position.Ticket();
         else if(m_position.PositionType() == POSITION_TYPE_SELL)
            m_shortTicket = m_position.Ticket();
      }
   }

   // Process positions based on count
   if(posNumber > 1)
   {
      Alert("Error: Multiple positions detected");
      return(false);
   }
   else if(posNumber == 1)
   {
      // Handle existing positions - check for closing conditions
      if(m_position.SelectByTicket(m_longTicket) && m_position.PositionType() == POSITION_TYPE_BUY)
      {
         if(LongClosed())
            return(true);
      }

      if(m_position.SelectByTicket(m_shortTicket) && m_position.PositionType() == POSITION_TYPE_SELL)
      {
         if(ShortClosed())
            return(true);
      }
   }
   else if(posNumber == 0)
   {
      // No positions - process strategy phases
      if(m_tradingPhase == PHASE_PASSIVE)
      {
         if(ProcessPassivePhase())
            return(true);
      }
      else
      {
         if(ProcessActivePhase())
            return(true);
      }
   }

   return(false);
  }
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit(void)
  {
//--- create all necessary objects
   if(!ExtExpert.Init())
      return(INIT_FAILED);
//--- succeed
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Process a new fractal and update fractal couple accordingly     |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessNewFractal(double price, bool isUp, datetime time)
{
   bool fractalUpdated = false;

   if(m_firstFractalPrice == 0) // No first fractal yet
   {
      m_firstFractalPrice = price;
      m_firstFractalIsUp = isUp;
      m_firstFractalTime = time;
      printf("First fractal set: %s at %.5f at %s", isUp ? "UP" : "DOWN", price, TimeToString(time, TIME_DATE|TIME_MINUTES));
      fractalUpdated = true;
   }
   else if(m_secondFractalPrice == 0) // Have first, need second
   {
      if(isUp != m_firstFractalIsUp) // Opposite direction - potential second fractal
      {
         m_secondFractalPrice = price;
         m_secondFractalIsUp = isUp;
         m_secondFractalTime = time;
         printf("Second fractal set: %s at %.5f at %s", isUp ? "UP" : "DOWN", price, TimeToString(time, TIME_DATE|TIME_MINUTES));
         fractalUpdated = true;
      }
      else // Same direction - update first fractal if more extreme
      {
         if((isUp && price > m_firstFractalPrice) || (!isUp && price < m_firstFractalPrice))
         {
            m_firstFractalPrice = price;
            m_firstFractalTime = time;
            printf("First fractal updated to more extreme: %s at %.5f at %s", isUp ? "UP" : "DOWN", price, TimeToString(time, TIME_DATE|TIME_MINUTES));
            fractalUpdated = true;
         }
      }
   }
   else // Have both fractals - update the appropriate one if more extreme
   {
      // Check if this is the same fractal we already have
      if((m_firstFractalPrice == price && m_firstFractalIsUp == isUp) ||
         (m_secondFractalPrice == price && m_secondFractalIsUp == isUp))
      {
         // Same fractal already exists, don't update timestamp - preserve original
         printf("Fractal %s at %.5f already exists, preserving original timestamp", isUp ? "UP" : "DOWN", price);
         return false;
      }

      // Determine current high and low
      double currentHigh = MathMax(m_firstFractalPrice, m_secondFractalPrice);
      double currentLow = MathMin(m_firstFractalPrice, m_secondFractalPrice);

      if(isUp && price > currentHigh) // New higher high
      {
         // Update the fractal that represents the current high
         if(m_firstFractalPrice >= m_secondFractalPrice)
         {
            m_firstFractalPrice = price;
            m_firstFractalTime = time;
            m_firstFractalIsUp = isUp;
         }
         else
         {
            m_secondFractalPrice = price;
            m_secondFractalTime = time;
            m_secondFractalIsUp = isUp;
         }
         printf("Updated high fractal to %.5f at %s", price, TimeToString(time, TIME_DATE|TIME_MINUTES));
         fractalUpdated = true;
      }
      else if(!isUp && price < currentLow) // New lower low
      {
         // Update the fractal that represents the current low
         if(m_firstFractalPrice <= m_secondFractalPrice)
         {
            m_firstFractalPrice = price;
            m_firstFractalTime = time;
            m_firstFractalIsUp = isUp;
         }
         else
         {
            m_secondFractalPrice = price;
            m_secondFractalTime = time;
            m_secondFractalIsUp = isUp;
         }
         printf("Updated low fractal to %.5f at %s", price, TimeToString(time, TIME_DATE|TIME_MINUTES));
         fractalUpdated = true;
      }
   }

   return fractalUpdated;
}

//+------------------------------------------------------------------+
//| Update Fibonacci range based on current fractal couple          |
//+------------------------------------------------------------------+
void CFibReversalExpert::UpdateFibonacciRangeFromFractals(void)
{
   if(m_firstFractalPrice == 0 || m_secondFractalPrice == 0)
      return;

   // Set high and low based on fractal prices
   m_fibHighPrice = MathMax(m_firstFractalPrice, m_secondFractalPrice);
   m_fibLowPrice = MathMin(m_firstFractalPrice, m_secondFractalPrice);

   // Update current fractal info for heartbeat
   m_currentHighFractal = m_fibHighPrice;
   m_currentLowFractal = m_fibLowPrice;

   // Determine which fractal is high and which is low
   if(m_firstFractalPrice == m_fibHighPrice)
   {
      m_currentHighIsUp = m_firstFractalIsUp;
      m_currentLowIsUp = m_secondFractalIsUp;
   }
   else
   {
      m_currentHighIsUp = m_secondFractalIsUp;
      m_currentLowIsUp = m_firstFractalIsUp;
   }

   printf("Fibonacci range updated: Low=%.5f, High=%.5f", m_fibLowPrice, m_fibHighPrice);
}

//+------------------------------------------------------------------+
//| Check for new 4H bar                                           |
//+------------------------------------------------------------------+
bool IsNew4HBar()
{
   static datetime last4HTime = 0;
   datetime current4HTime = iTime(NULL, PERIOD_H4, 0);

   if(current4HTime != last4HTime)
   {
      last4HTime = current4HTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Expert tick handler                                             |
//+------------------------------------------------------------------+
void OnTick(void)
{
   // For 4H-based strategy, process on new 4H bars for fractal detection
   // But still check every tick for touch detection in active phase
   static int tickCounter = 0;
   tickCounter++;

   bool shouldProcess = false;

   // Always process on new 4H bars
   if(IsNew4HBar())
   {
      shouldProcess = true;
      printf("DEBUG: New 4H bar detected, processing strategy");
   }

   // In active phase, process more frequently for touch detection
   if(ExtExpert.GetTradingPhase() != PHASE_PASSIVE)
   {
      if(tickCounter % 10 == 0) // Every 10 ticks in active phase
         shouldProcess = true;
   }

   // Process heartbeat and position management every 100 ticks
   if(tickCounter % 100 == 0)
      shouldProcess = true;

   if(shouldProcess)
      ExtExpert.Processing();
}
