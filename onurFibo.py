import pandas as pd
import os
 
# --- AYARLAR ---
VERI_DOSYASI_ADI = 'nas100_4h_2020_data.txt'
CIKTI_EXCEL_ADI = 'strateji_son_versiyon_karli_10_nas.xlsx'
MIN_YUZDE_FARK = 0.03
BASLANGIC_SERMAYESI = 100
KALDIRAC = 15
HARD_STOP_YUZDE = 0.015
 
class FibonacciRetracement:
    def _init_(self, tepe_fiyat, tepe_zaman, dip_fiyat, dip_zaman, fib_type):
        self.type = fib_type
        self.tepe_fiyat, self.tepe_zaman = tepe_fiyat, tepe_zaman
        self.dip_fiyat, self.dip_zaman = dip_fiyat, dip_zaman
        diff = abs(self.tepe_fiyat - self.dip_fiyat)
        if self.type == 'Yükseliş':
            self.level_236 = self.dip_fiyat + 0.236 * diff
            self.level_500 = self.dip_fiyat + 0.5 * diff
        else:
            self.level_236 = self.tepe_fiyat - 0.236 * diff
            self.level_500 = self.tepe_fiyat - 0.5 * diff
 
def veri_yukle(dosya_adi):
    if not os.path.exists(dosya_adi):
        print(f"❌ HATA: '{dosya_adi}' dosyası bulunamadı.")
        return None
    df = pd.read_csv(dosya_adi)
    df['Timestamp'] = pd.to_datetime(df['timestamp'])
    df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}, inplace=True)
    return df.set_index('Timestamp')[['Open', 'High', 'Low', 'Close']]
 
def stratejiyi_calistir(df):
    sonuclar = []
    mode = 'Yükseliş Fibi Arıyor'
    aktif_fib, aday_fib = None, None
    kilitli_tepe_fiyat, kilitli_tepe_zaman = 0, None
    kilitli_dip_fiyat, kilitli_dip_zaman = float('inf'), None
    gecici_tepe_kilit, gecici_dip_kilit = None, None
    sermaye = BASLANGIC_SERMAYESI
 
    for i in range(2, len(df)):
        row = df.iloc[i]
        current_time = df.index[i]
        close0, close1, close2 = df['Close'].iloc[i-2:i+1]
 
        if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor':
            mode = 'Yükseliş Fibi Arıyor'
            kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
            kilitli_tepe_zaman = df['High'].iloc[i-2:i+1].idxmax()
        elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor':
            mode = 'Düşüş Fibi Arıyor'
            kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
            kilitli_dip_zaman = df['Low'].iloc[i-2:i+1].idxmin()
 
        if aktif_fib:
            success, fail = False, False
            kar_orani = 0
 
            if aktif_fib.type == 'Yükseliş':
                if row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE):
                    fail = True
                    kar_orani = -HARD_STOP_YUZDE * 100
                elif row['High'] >= aktif_fib.level_500:
                    success = True
                    kar_orani = (aktif_fib.level_500 - aktif_fib.level_236) / aktif_fib.level_236 * 100
            else:
                if row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE):
                    fail = True
                    kar_orani = -HARD_STOP_YUZDE * 100
                elif row['Low'] <= aktif_fib.level_500:
                    success = True
                    kar_orani = (aktif_fib.level_236 - aktif_fib.level_500) / aktif_fib.level_236 * 100
 
            if success or fail:
                sermaye *= 1 + (kar_orani / 100) * KALDIRAC
                sonuclar.append({
                    'Olay Tarihi': current_time,
                    'Olay': 'Fib Tamamlandı' if success else 'Hard Stop',
                    'Fib Tipi': aktif_fib.type,
                    'Fib Tepe Mumu': aktif_fib.tepe_zaman,
                    'Fib Dip Mumu': aktif_fib.dip_zaman,
                    'İşlem Kar/Stop (%)': round(kar_orani, 2),
                    'Kumulatif Sermaye ($)': round(sermaye, 2)
                })
                aktif_fib = None
            continue
 
        if aday_fib:
            if aday_fib.type == 'Yükseliş':
                if row['Close'] < aday_fib.dip_fiyat or row['Close'] > aday_fib.tepe_fiyat:
                    aday_fib = None
                elif row['Close'] > aday_fib.level_236:
                    aktif_fib = aday_fib
                    aday_fib = None
            else:
                if row['Close'] > aday_fib.tepe_fiyat or row['Close'] < aday_fib.dip_fiyat:
                    aday_fib = None
                elif row['Close'] < aday_fib.level_236:
                    aktif_fib = aday_fib
                    aday_fib = None
            continue
 
        if mode == 'Yükseliş Fibi Arıyor':
            if gecici_tepe_kilit:
                kilitli_tepe_fiyat, kilitli_tepe_zaman = gecici_tepe_kilit
                gecici_tepe_kilit = None
            if row['High'] > kilitli_tepe_fiyat:
                kilitli_tepe_fiyat, kilitli_tepe_zaman = row['High'], current_time
            elif kilitli_tepe_zaman:
                fark = (kilitli_tepe_fiyat - row['Low']) / row['Low']
                if fark >= MIN_YUZDE_FARK:
                    aday_fib = FibonacciRetracement(kilitli_tepe_fiyat, kilitli_tepe_zaman, row['Low'], current_time, 'Yükseliş')
 
        elif mode == 'Düşüş Fibi Arıyor':
            if gecici_dip_kilit:
                kilitli_dip_fiyat, kilitli_dip_zaman = gecici_dip_kilit
                gecici_dip_kilit = None
            if row['Low'] < kilitli_dip_fiyat:
                kilitli_dip_fiyat, kilitli_dip_zaman = row['Low'], current_time
            elif kilitli_dip_zaman:
                fark = (row['High'] - kilitli_dip_fiyat) / kilitli_dip_fiyat
                if fark >= MIN_YUZDE_FARK:
                    aday_fib = FibonacciRetracement(row['High'], current_time, kilitli_dip_fiyat, kilitli_dip_zaman, 'Düşüş')
 
    return sonuclar
 
if _name_ == '_main_':
    df = veri_yukle(VERI_DOSYASI_ADI)
    if df is not None:
        sonuclar = stratejiyi_calistir(df)
        if sonuclar:
            pd.DataFrame(sonuclar).to_excel(CIKTI_EXCEL_ADI, index=False)
            print(f"✔️ Sonuçlar kaydedildi: {CIKTI_EXCEL_ADI}")
    input("Kapatmak için Enter'a bas...")